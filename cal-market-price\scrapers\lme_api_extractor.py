from playwright.sync_api import sync_playwright
import json
import csv
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import re
import requests

class LMEAPIExtractor:
    """
    Extract LME steel rebar data by capturing API endpoints and processing JSON responses
    """
    
    def __init__(self):
        self.steel_rebar_url = 'https://www.lme.com/Metals/Ferrous/LME-Steel-Rebar-FOB-Turkey-Platts#Price+graph'
        # Use relative path from current file location
        self.output_dir = Path(__file__).parent.parent / 'data'
        self.lme_data_dir = self.output_dir / 'lme_data'

        # Create directories (no raw_dir needed)
        for dir_path in [self.output_dir, self.lme_data_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def extract_api_data(self):
        """
        Capture API endpoints and extract JSON data from LME steel rebar page
        """
        print("🔍 LME API Endpoint Extractor")
        print("=" * 50)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            # Storage for captured data
            api_endpoints = []
            json_responses = []
            
            # Network request handler
            def handle_request(request):
                url = request.url
                # Look for API endpoints
                if any(keyword in url.lower() for keyword in ['api', 'data', 'chart', 'trading', 'price']):
                    api_info = {
                        'url': url,
                        'method': request.method,
                        'headers': dict(request.headers),
                        'timestamp': datetime.now().isoformat()
                    }
                    api_endpoints.append(api_info)
                    print(f"🎯 API Endpoint Found: {url}")
            
            # Network response handler
            def handle_response(response):
                url = response.request.url
                if any(keyword in url.lower() for keyword in ['api', 'data', 'chart', 'trading', 'price']):
                    try:
                        content_type = response.headers.get('content-type', '').lower()
                        if 'json' in content_type:
                            json_data = response.json()
                            response_info = {
                                'url': url,
                                'status': response.status,
                                'content_type': content_type,
                                'data': json_data,
                                'timestamp': datetime.now().isoformat()
                            }
                            json_responses.append(response_info)
                            print(f"✅ JSON Response Captured: {url} (Status: {response.status})")
                    except Exception as e:
                        print(f"⚠️ Could not parse JSON from {url}: {e}")
            
            # Set up network monitoring
            page.on('request', handle_request)
            page.on('response', handle_response)
            
            try:
                # Navigate to LME steel rebar page
                print(f"📡 Loading: {self.steel_rebar_url}")
                page.goto(self.steel_rebar_url, wait_until='networkidle')
                
                # Wait for initial load
                print("⏳ Waiting for page to load completely...")
                page.wait_for_timeout(5000)

                # Check if we already have the chart data we need
                chart_data_found = any('chart-data' in resp['url'] and resp.get('data')
                                     for resp in json_responses)

                if chart_data_found:
                    print("✅ Chart data already captured, processing...")
                else:
                    # Try to trigger chart data loading
                    print("🔄 Triggering chart data loads...")
                    self._trigger_chart_loads(page)

                    # Wait for additional API calls (shorter timeout)
                    page.wait_for_timeout(3000)
                
                # Process captured data
                print(f"\n📊 Processing captured data...")
                print(f"API Endpoints found: {len(api_endpoints)}")
                print(f"JSON Responses captured: {len(json_responses)}")

                # Save and process the data
                price_data = self._process_captured_data(api_endpoints, json_responses)

                # Check if we got good price data
                if price_data and len(price_data) > 0:
                    print(f"✅ Successfully extracted {len(price_data)} price points")

                    # Process the data into CSV files using the quick update logic
                    self._process_to_csv_files(price_data)
                else:
                    print("⚠️ No price data extracted from captured responses")
                
                browser.close()
                return price_data
                
            except Exception as e:
                print(f"❌ Error during extraction: {e}")
                browser.close()
                raise
    
    def _trigger_chart_loads(self, page):
        """Try to trigger chart data loading by interacting with page elements (optimized)"""
        try:
            # Quick scroll to trigger any lazy loading
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            page.wait_for_timeout(1000)
            page.evaluate("window.scrollTo(0, 0)")
            page.wait_for_timeout(1000)

            # Look for chart containers (limit to first few)
            chart_selectors = [
                '[class*="chart"]',
                '.highcharts-container'
            ]

            for selector in chart_selectors:
                elements = page.query_selector_all(selector)
                for element in elements[:1]:  # Try only first element
                    try:
                        element.hover()
                        page.wait_for_timeout(500)
                        element.click()
                        page.wait_for_timeout(1000)
                        break  # Exit after first successful interaction
                    except:
                        pass

            # Look for common date range buttons (quick check)
            date_buttons = page.query_selector_all('button')
            for button in date_buttons[:5]:  # Check only first 5 buttons
                try:
                    text = button.inner_text().lower()
                    if any(period in text for period in ['1y', 'year']):
                        button.click()
                        page.wait_for_timeout(2000)
                        break
                except:
                    pass

        except Exception as e:
            print(f"⚠️ Error triggering chart loads: {e}")
    
    def _process_captured_data(self, api_endpoints, json_responses):
        """Process captured API data and extract price information"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save JSON responses in lme_data folder (skip API endpoints file)
        json_file = self.lme_data_dir / f'lme_json_responses_{timestamp}.json'
        with open(json_file, 'w') as f:
            json.dump(json_responses, f, indent=2, default=str)
        print(f"💾 Saved JSON responses: {json_file}")
        
        # Extract price data from JSON responses using the working logic
        price_data = []

        for response in json_responses:
            if 'chart-data' in response.get('url', ''):
                try:
                    api_data = response['data']
                    prices = self._extract_price_data_from_lme_api(api_data)
                    if prices:
                        price_data.extend(prices)
                except Exception as e:
                    print(f"⚠️ Error processing chart data: {e}")
        
        # Return the raw price data for further processing
        return price_data
        
        # Skip creating summary file - just print summary
        
        print(f"\n📈 EXTRACTION SUMMARY:")
        print(f"API endpoints found: {len(api_endpoints)}")
        print(f"JSON responses: {len(json_responses)}")
        print(f"Price data points: {len(price_data)}")
        if latest_price:
            return price_data

    def _extract_price_data_from_lme_api(self, api_data):
        """Extract price data from LME API response (working logic from quick update)"""
        price_data = []

        try:
            labels = api_data.get('Labels', [])
            datasets = api_data.get('Datasets', [])

            print(f"📊 Found {len(labels)} dates and {len(datasets)} datasets")

            # Process ALL contract types (let user choose later)
            contract_priority_map = {
                'Month 1': 1, 'Month 2': 2, 'Month 3': 3, 'Month 4': 4, 'Month 5': 5, 'Month 6': 6,
                'Month 7': 7, 'Month 8': 8, 'Month 9': 9, 'Month 10': 10, 'Month 11': 11, 'Month 12': 12,
                'Month 13': 13, 'Month 14': 14, 'Month 15': 15
            }

            for dataset in datasets:
                if dataset.get('Label') == 'Price':
                    prices = dataset.get('Data', [])
                    row_title = dataset.get('RowTitle', '')

                    # Process all contract types
                    if row_title in contract_priority_map:
                        priority = contract_priority_map[row_title]
                        print(f"💰 Processing: {row_title} (Priority: {priority})")

                        # Combine dates with prices
                        for date_str, price_str in zip(labels, prices):
                            try:
                                # Parse date (format: MM/DD/YYYY)
                                date_obj = datetime.strptime(date_str, '%m/%d/%Y')
                                formatted_date = date_obj.strftime('%Y-%m-%d')

                                # Parse price
                                price_value = float(price_str)

                                price_data.append({
                                    'date': formatted_date,
                                    'price_usd_ton': price_value,
                                    'source': 'LME_Steel_Rebar_FOB_Turkey',
                                    'contract': row_title,
                                    'contract_priority': priority,
                                    'extraction_timestamp': datetime.now().isoformat()
                                })

                            except (ValueError, TypeError) as e:
                                continue
                    else:
                        print(f"⏭️ Skipping: {row_title} (unknown contract type)")

            # Sort by date
            price_data.sort(key=lambda x: x['date'])

            # Filter to get current/recent prices (keep all contracts)
            from datetime import timedelta
            today = datetime.now()
            current_prices = [p for p in price_data
                            if datetime.strptime(p['date'], '%Y-%m-%d') <= today + timedelta(days=30)]

            if current_prices:
                # Find 1-month contract for default display
                month1_prices = [p for p in current_prices if p.get('contract') == 'Month 1']
                if month1_prices:
                    latest = month1_prices[-1]
                    print(f"🎯 Default Price (1-Month Contract): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
                else:
                    latest = current_prices[-1]
                    print(f"🎯 Latest Price ({latest.get('contract', 'Unknown')}): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")

                # Keep all current contract data (don't filter to just 1-month)
                price_data = current_prices

                # Show available contracts
                available_contracts = sorted(set(p['contract'] for p in current_prices),
                                           key=lambda x: int(x.split()[1]) if 'Month' in x else 999)
                print(f"📋 Available Contracts: {', '.join(available_contracts)}")

            elif price_data:
                latest = price_data[-1]
                print(f"🎯 Latest Available ({latest.get('contract', 'Unknown')}): ${latest['price_usd_ton']:.2f}/ton ({latest['date']})")
            else:
                print("❌ No price data found")

        except Exception as e:
            print(f"❌ Error extracting price data: {e}")

        return price_data

    def _process_to_csv_files(self, price_data):
        """Process price data into CSV files (logic from quick update)"""
        if not price_data:
            print("❌ No price data to process")
            return

        # Save/update detailed price data as single CSV file
        df = pd.DataFrame(price_data)

        # Ensure all important columns are present and ordered logically
        column_order = ['date', 'price_usd_ton', 'contract', 'contract_priority', 'source', 'extraction_timestamp']
        df = df.reindex(columns=column_order)

        csv_file = self.lme_data_dir / 'steel_rebar_prices.csv'
        df.to_csv(csv_file, index=False)
        print(f"💾 Updated steel rebar prices: {csv_file.name} (with contract details)")

        # Save latest price for easy access by rebar calculator
        latest_price = price_data[-1]
        latest_df = pd.DataFrame([{
            'date': latest_price['date'],
            'price_usd_ton': latest_price['price_usd_ton'],
            'source': 'LME_Steel_Rebar_FOB_Turkey'
        }])
        latest_file = self.lme_data_dir / 'latest_steel_price.csv'
        latest_df.to_csv(latest_file, index=False)
        print(f"💾 Updated latest price: {latest_file.name}")

        # Save in detailed format for integration (keep all important columns)
        history_df = df[['date', 'price_usd_ton', 'contract', 'contract_priority', 'source', 'extraction_timestamp']].copy()

        # Sort by date and contract priority (1-month first)
        history_df = history_df.sort_values(['date', 'contract_priority'])

        # Remove duplicates by keeping the highest priority contract per date
        history_df = history_df.drop_duplicates(subset=['date'], keep='first')

        # Rename columns for clarity
        history_df = history_df.rename(columns={
            'price_usd_ton': 'value',
            'contract': 'lme_contract',
            'contract_priority': 'priority',
            'extraction_timestamp': 'last_updated'
        })

        history_file = self.lme_data_dir / 'LME_Steel_Rebar_data.csv'
        history_df.to_csv(history_file, index=False)
        print(f"💾 Updated price history: {history_file.name} (with contract details)")

        print(f"\n🎯 UPDATED DATA SUMMARY:")
        print(f"Default FOB Price: ${latest_price['price_usd_ton']:.2f}/ton (1-Month Contract)")
        print(f"Date: {latest_price['date']}")
        print(f"Total Data Points: {len(price_data)} (all available contracts)")

        # Show contract breakdown
        contract_counts = {}
        for item in price_data:
            contract = item.get('contract', 'Unknown')
            contract_counts[contract] = contract_counts.get(contract, 0) + 1

        print(f"Contract Breakdown:")
        for contract in sorted(contract_counts.keys(), key=lambda x: int(x.split()[1]) if 'Month' in x else 999):
            count = contract_counts[contract]
            print(f"  {contract}: {count} data points")

    def _extract_prices_from_json(self, json_data):
        """Extract price information from JSON data"""
        prices = []
        
        try:
            # Handle different JSON structures
            if isinstance(json_data, dict):
                # Look for common price data structures
                for key in ['data', 'series', 'prices', 'values', 'chartData']:
                    if key in json_data:
                        prices.extend(self._parse_price_array(json_data[key]))
                
                # Look for direct price values
                for key, value in json_data.items():
                    if 'price' in key.lower() and isinstance(value, (int, float)):
                        if 200 <= value <= 1000:  # Reasonable steel price range
                            prices.append({
                                'price': value,
                                'date': datetime.now().strftime('%Y-%m-%d'),
                                'field': key
                            })
            
            elif isinstance(json_data, list):
                prices.extend(self._parse_price_array(json_data))
                
        except Exception as e:
            print(f"⚠️ Error extracting prices from JSON: {e}")
        
        return prices
    
    def _parse_price_array(self, data_array):
        """Parse price data from array structures"""
        prices = []
        
        try:
            if isinstance(data_array, list):
                for item in data_array:
                    if isinstance(item, dict):
                        # Look for price and date fields
                        price_val = None
                        date_val = None
                        
                        for key, value in item.items():
                            if isinstance(value, (int, float)) and 200 <= value <= 1000:
                                if 'price' in key.lower() or 'value' in key.lower() or 'y' == key.lower():
                                    price_val = value
                            elif 'date' in key.lower() or 'time' in key.lower() or 'x' == key.lower():
                                date_val = value
                        
                        if price_val:
                            prices.append({
                                'price': price_val,
                                'date': self._format_date(date_val) if date_val else datetime.now().strftime('%Y-%m-%d'),
                                'raw_item': item
                            })
                    
                    elif isinstance(item, (list, tuple)) and len(item) >= 2:
                        # Handle [timestamp, price] format
                        try:
                            if isinstance(item[1], (int, float)) and 200 <= item[1] <= 1000:
                                prices.append({
                                    'price': item[1],
                                    'date': self._format_date(item[0]),
                                    'raw_item': item
                                })
                        except:
                            pass
                            
        except Exception as e:
            print(f"⚠️ Error parsing price array: {e}")
        
        return prices
    
    def _format_date(self, date_val):
        """Format various date formats to YYYY-MM-DD"""
        try:
            if isinstance(date_val, (int, float)):
                # Assume timestamp (milliseconds or seconds)
                if date_val > 1e12:  # Milliseconds
                    date_val = date_val / 1000
                dt = datetime.fromtimestamp(date_val)
                return dt.strftime('%Y-%m-%d')
            elif isinstance(date_val, str):
                # Try to parse string date
                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%d/%m/%Y', '%m/%d/%Y']:
                    try:
                        dt = datetime.strptime(date_val, fmt)
                        return dt.strftime('%Y-%m-%d')
                    except:
                        continue
        except:
            pass
        
        return datetime.now().strftime('%Y-%m-%d')

def main():
    extractor = LMEAPIExtractor()
    
    try:
        price_data = extractor.extract_api_data()
        
        if price_data:
            print(f"\n✅ Successfully extracted {len(price_data)} price data points!")
        else:
            print(f"\n⚠️ No price data extracted. Check the captured API endpoints.")
            
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")

if __name__ == "__main__":
    main()
